// @ts-nocheck
/* eslint-disable */
/* tslint:disable */
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: aukbrhkegRkQ6KizvhdUPT
// Component: YldGgVsq6N

import * as React from "react";

import {
  Flex as Flex__,
  SingleBooleanChoiceArg,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  hasVariant,
  useDollarState,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import LeftPaneHeader from "../../components/studio/LeftPaneHeader"; // plasmic-import: XLa52PvduIy/component
import LeftSearchPanel from "../../components/studio/LeftSearchPanel"; // plasmic-import: TqAPn0srTq/component
import Button from "../../components/widgets/Button"; // plasmic-import: SEF-sRmSoqV5c/component

import "@plasmicapp/react-web/lib/plasmic.css";

import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import plasmic_plasmic_kit_new_design_system_former_style_controls_css from "../plasmic_kit_style_controls/plasmic_plasmic_kit_styles_pane.module.css"; // plasmic-import: gYEVvAzCcLMHDVPvuYxkFh/projectcss
import plasmic_plasmic_kit_design_system_deprecated_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import projectcss from "../PP__plasmickit_left_pane.module.css"; // plasmic-import: aukbrhkegRkQ6KizvhdUPT/projectcss
import sty from "./PlasmicLeftVersionsPanel.module.css"; // plasmic-import: YldGgVsq6N/css

import ChevronDownSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ChevronDownSvg"; // plasmic-import: xZrB9_0ir/icon
import StampSvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__StampSvg"; // plasmic-import: F2ZajSMKM/icon

createPlasmicElementProxy;

export type PlasmicLeftVersionsPanel__VariantMembers = {
  showAlert: "showAlert";
};
export type PlasmicLeftVersionsPanel__VariantsArgs = {
  showAlert?: SingleBooleanChoiceArg<"showAlert">;
};
type VariantPropType = keyof PlasmicLeftVersionsPanel__VariantsArgs;
export const PlasmicLeftVersionsPanel__VariantProps =
  new Array<VariantPropType>("showAlert");

export type PlasmicLeftVersionsPanel__ArgsType = {};
type ArgPropType = keyof PlasmicLeftVersionsPanel__ArgsType;
export const PlasmicLeftVersionsPanel__ArgProps = new Array<ArgPropType>();

export type PlasmicLeftVersionsPanel__OverridesType = {
  root?: Flex__<"div">;
  leftSearchPanel?: Flex__<typeof LeftSearchPanel>;
  versionsHeader?: Flex__<typeof LeftPaneHeader>;
  publishButton?: Flex__<typeof Button>;
  content?: Flex__<"div">;
};

export interface DefaultLeftVersionsPanelProps {
  showAlert?: SingleBooleanChoiceArg<"showAlert">;
  className?: string;
}

const $$ = {};

function PlasmicLeftVersionsPanel__RenderFunc(props: {
  variants: PlasmicLeftVersionsPanel__VariantsArgs;
  args: PlasmicLeftVersionsPanel__ArgsType;
  overrides: PlasmicLeftVersionsPanel__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(
    () =>
      Object.assign(
        {},
        Object.fromEntries(
          Object.entries(props.args).filter(([_, v]) => v !== undefined)
        )
      ),
    [props.args]
  );

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "showAlert",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.showAlert,
      },
    ],

    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  return (
    <div
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_deprecated_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        plasmic_plasmic_kit_new_design_system_former_style_controls_css.plasmic_tokens,
        sty.root,
        { [sty.rootshowAlert]: hasVariant($state, "showAlert", "showAlert") }
      )}
    >
      <LeftSearchPanel
        data-plasmic-name={"leftSearchPanel"}
        data-plasmic-override={overrides.leftSearchPanel}
        className={classNames("__wab_instance", sty.leftSearchPanel, {
          [sty.leftSearchPanelshowAlert]: hasVariant(
            $state,
            "showAlert",
            "showAlert"
          ),
        })}
      />

      <LeftPaneHeader
        data-plasmic-name={"versionsHeader"}
        data-plasmic-override={overrides.versionsHeader}
        actions={
          <Button
            data-plasmic-name={"publishButton"}
            data-plasmic-override={overrides.publishButton}
            endIcon={
              <ChevronDownSvgIcon
                className={classNames(projectcss.all, sty.svg__mRQha)}
                role={"img"}
              />
            }
            size={"wide"}
            startIcon={
              <StampSvgIcon
                className={classNames(projectcss.all, sty.svg__g3KFb)}
                role={"img"}
              />
            }
            type={["secondary"]}
            withIcons={["startIcon"]}
          >
            {"Publish project"}
          </Button>
        }
        className={classNames("__wab_instance", sty.versionsHeader, {
          [sty.versionsHeadershowAlert]: hasVariant(
            $state,
            "showAlert",
            "showAlert"
          ),
        })}
        description={
          "Publishing a version of the project lets you restore it later, lets it be published in your production codebase, and lets other projects import to reuse its components/assets."
        }
        showAlert={
          hasVariant($state, "showAlert", "showAlert") ? true : undefined
        }
        title={"Published Versions"}
      />

      <div
        data-plasmic-name={"content"}
        data-plasmic-override={overrides.content}
        className={classNames(projectcss.all, sty.content)}
      />
    </div>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "leftSearchPanel",
    "versionsHeader",
    "publishButton",
    "content",
  ],

  leftSearchPanel: ["leftSearchPanel"],
  versionsHeader: ["versionsHeader", "publishButton"],
  publishButton: ["publishButton"],
  content: ["content"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  leftSearchPanel: typeof LeftSearchPanel;
  versionsHeader: typeof LeftPaneHeader;
  publishButton: typeof Button;
  content: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicLeftVersionsPanel__OverridesType,
  DescendantsType<T>
>;

type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicLeftVersionsPanel__VariantsArgs;
    args?: PlasmicLeftVersionsPanel__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicLeftVersionsPanel__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicLeftVersionsPanel__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicLeftVersionsPanel__ArgProps,
          internalVariantPropNames: PlasmicLeftVersionsPanel__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicLeftVersionsPanel__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicLeftVersionsPanel";
  } else {
    func.displayName = `PlasmicLeftVersionsPanel.${nodeName}`;
  }
  return func;
}

export const PlasmicLeftVersionsPanel = Object.assign(
  // Top-level PlasmicLeftVersionsPanel renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    leftSearchPanel: makeNodeComponent("leftSearchPanel"),
    versionsHeader: makeNodeComponent("versionsHeader"),
    publishButton: makeNodeComponent("publishButton"),
    content: makeNodeComponent("content"),

    // Metadata about props expected for PlasmicLeftVersionsPanel
    internalVariantProps: PlasmicLeftVersionsPanel__VariantProps,
    internalArgProps: PlasmicLeftVersionsPanel__ArgProps,
  }
);

export default PlasmicLeftVersionsPanel;
/* prettier-ignore-end */
