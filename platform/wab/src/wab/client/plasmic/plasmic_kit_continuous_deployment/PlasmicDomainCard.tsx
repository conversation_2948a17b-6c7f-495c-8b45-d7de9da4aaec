// @ts-nocheck
/* eslint-disable */
/* tslint:disable */
/* prettier-ignore-start */

/** @jsxRuntime classic */
/** @jsx createPlasmicElementProxy */
/** @jsxFrag React.Fragment */

// This class is auto-generated by Plasmic; please do not edit!
// Plasmic Project: fpbcKyXdMTvY59T4C5fjcC
// Component: eqF_n5a1-6b

import * as React from "react";

import {
  Flex as Flex__,
  SingleBooleanChoiceArg,
  SingleChoiceArg,
  Stack as Stack__,
  StrictProps,
  classNames,
  createPlasmicElementProxy,
  deriveRenderOpts,
  hasVariant,
  useDollarState,
} from "@plasmicapp/react-web";
import { useDataEnv } from "@plasmicapp/react-web/lib/host";

import ErrorFeedback from "../../components/github/ErrorFeedback"; // plasmic-import: 6ztKJ9-EG9Y/component
import Button from "../../components/widgets/Button"; // plasmic-import: SEF-sRmSoqV5c/component
import IconButton from "../../components/widgets/IconButton"; // plasmic-import: LPry-TF4j22a/component

import "@plasmicapp/react-web/lib/plasmic.css";

import projectcss from "../../components/modals/plasmic/plasmic_kit_project_settings/plasmic_plasmic_kit_project_settings.module.css"; // plasmic-import: fpbcKyXdMTvY59T4C5fjcC/projectcss
import plasmic_plasmic_kit_design_system_css from "../PP__plasmickit_design_system.module.css"; // plasmic-import: tXkSR39sgCDWSitZxC5xFV/projectcss
import plasmic_plasmic_kit_color_tokens_css from "../plasmic_kit_q_4_color_tokens/plasmic_plasmic_kit_q_4_color_tokens.module.css"; // plasmic-import: 95xp9cYcv7HrNWpFWWhbcv/projectcss
import sty from "./PlasmicDomainCard.module.css"; // plasmic-import: eqF_n5a1-6b/css

import CheckCirclesvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__CheckCircleSvg"; // plasmic-import: h7sB2KeL-/icon
import ChevronDownsvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ChevronDownSvg"; // plasmic-import: xZrB9_0ir/icon
import RefreshsvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__RefreshSvg"; // plasmic-import: PEaq_S7gQ/icon
import SharesvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__ShareSvg"; // plasmic-import: vRB2dtcKk/icon
import Trash2SvgIcon from "../plasmic_kit_icons/icons/PlasmicIcon__Trash2Svg"; // plasmic-import: nS4_I75qv/icon

createPlasmicElementProxy;

export type PlasmicDomainCard__VariantMembers = {
  refreshing: "refreshing";
  error: "cname" | "apex" | "success" | "error";
  secondary: "secondary";
};
export type PlasmicDomainCard__VariantsArgs = {
  refreshing?: SingleBooleanChoiceArg<"refreshing">;
  error?: SingleChoiceArg<"cname" | "apex" | "success" | "error">;
  secondary?: SingleBooleanChoiceArg<"secondary">;
};
type VariantPropType = keyof PlasmicDomainCard__VariantsArgs;
export const PlasmicDomainCard__VariantProps = new Array<VariantPropType>(
  "refreshing",
  "error",
  "secondary"
);

export type PlasmicDomainCard__ArgsType = {};
type ArgPropType = keyof PlasmicDomainCard__ArgsType;
export const PlasmicDomainCard__ArgProps = new Array<ArgPropType>();

export type PlasmicDomainCard__OverridesType = {
  root?: Flex__<"div">;
  label?: Flex__<"div">;
  customDomainLabel?: Flex__<"div">;
  openButton?: Flex__<typeof IconButton>;
  errorFeedback?: Flex__<typeof ErrorFeedback>;
  domainErrorMessage4?: Flex__<"div">;
  refreshButton?: Flex__<typeof Button>;
  removeButton?: Flex__<typeof Button>;
  cnameTab?: Flex__<"button">;
  apexTab?: Flex__<"button">;
  apexRow?: Flex__<"div">;
  name?: Flex__<"div">;
  value?: Flex__<"div">;
  cnameRow?: Flex__<"div">;
};

export interface DefaultDomainCardProps {
  refreshing?: SingleBooleanChoiceArg<"refreshing">;
  error?: SingleChoiceArg<"cname" | "apex" | "success" | "error">;
  secondary?: SingleBooleanChoiceArg<"secondary">;
  className?: string;
}

const $$ = {};

function PlasmicDomainCard__RenderFunc(props: {
  variants: PlasmicDomainCard__VariantsArgs;
  args: PlasmicDomainCard__ArgsType;
  overrides: PlasmicDomainCard__OverridesType;
  forNode?: string;
}) {
  const { variants, overrides, forNode } = props;

  const args = React.useMemo(() => Object.assign({}, props.args), [props.args]);

  const $props = {
    ...args,
    ...variants,
  };

  const $ctx = useDataEnv?.() || {};
  const refsRef = React.useRef({});
  const $refs = refsRef.current;

  const stateSpecs: Parameters<typeof useDollarState>[0] = React.useMemo(
    () => [
      {
        path: "refreshing",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.refreshing,
      },
      {
        path: "error",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.error,
      },
      {
        path: "secondary",
        type: "private",
        variableType: "variant",
        initFunc: ({ $props, $state, $queries, $ctx }) => $props.secondary,
      },
    ],

    [$props, $ctx, $refs]
  );
  const $state = useDollarState(stateSpecs, {
    $props,
    $ctx,
    $queries: {},
    $refs,
  });

  return (
    <Stack__
      as={"div"}
      data-plasmic-name={"root"}
      data-plasmic-override={overrides.root}
      data-plasmic-root={true}
      data-plasmic-for-node={forNode}
      hasGap={true}
      className={classNames(
        projectcss.all,
        projectcss.root_reset,
        projectcss.plasmic_default_styles,
        projectcss.plasmic_mixins,
        projectcss.plasmic_tokens,
        plasmic_plasmic_kit_design_system_css.plasmic_tokens,
        plasmic_plasmic_kit_color_tokens_css.plasmic_tokens,
        sty.root,
        {
          [sty.rooterror_apex]: hasVariant($state, "error", "apex"),
          [sty.rooterror_cname]: hasVariant($state, "error", "cname"),
          [sty.rooterror_error]: hasVariant($state, "error", "error"),
          [sty.rootrefreshing]: hasVariant($state, "refreshing", "refreshing"),
        }
      )}
    >
      <Stack__
        as={"div"}
        hasGap={true}
        className={classNames(projectcss.all, sty.freeBox___7Vjq, {
          [sty.freeBoxerror_apex_secondary___7VjqWIbN7ZeL6W]:
            hasVariant($state, "secondary", "secondary") &&
            hasVariant($state, "error", "apex"),
          [sty.freeBoxsecondary___7VjqZeL6W]: hasVariant(
            $state,
            "secondary",
            "secondary"
          ),
        })}
      >
        <div
          data-plasmic-name={"label"}
          data-plasmic-override={overrides.label}
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.label,
            { [sty.labelerror_apex]: hasVariant($state, "error", "apex") }
          )}
        >
          {"Custom domain"}
        </div>
        <Stack__
          as={"div"}
          hasGap={true}
          className={classNames(projectcss.all, sty.freeBox__iNuFl)}
        >
          <div
            data-plasmic-name={"customDomainLabel"}
            data-plasmic-override={overrides.customDomainLabel}
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.customDomainLabel,
              {
                [sty.customDomainLabelerror_success]: hasVariant(
                  $state,
                  "error",
                  "success"
                ),
                [sty.customDomainLabelrefreshing]: hasVariant(
                  $state,
                  "refreshing",
                  "refreshing"
                ),
              }
            )}
          >
            {"www.foobarfoobarfoobarfoobar.com"}
          </div>
          <IconButton
            data-plasmic-name={"openButton"}
            data-plasmic-override={overrides.openButton}
            className={classNames("__wab_instance", sty.openButton, {
              [sty.openButtonerror_success]: hasVariant(
                $state,
                "error",
                "success"
              ),
            })}
            withBackgroundHover={true}
          >
            <SharesvgIcon
              className={classNames(projectcss.all, sty.svg__eIAp)}
              role={"img"}
            />
          </IconButton>
        </Stack__>
      </Stack__>
      <Stack__
        as={"div"}
        hasGap={true}
        className={classNames(projectcss.all, sty.freeBox__gsDpH, {
          [sty.freeBoxerror_apex__gsDpHwIbN7]: hasVariant(
            $state,
            "error",
            "apex"
          ),
          [sty.freeBoxerror_cname__gsDpHFiouZ]: hasVariant(
            $state,
            "error",
            "cname"
          ),
          [sty.freeBoxerror_error__gsDpHByfXx]: hasVariant(
            $state,
            "error",
            "error"
          ),
          [sty.freeBoxerror_success__gsDpHsUyAh]: hasVariant(
            $state,
            "error",
            "success"
          ),
          [sty.freeBoxrefreshing__gsDpH1Rn0P]: hasVariant(
            $state,
            "refreshing",
            "refreshing"
          ),
        })}
      >
        <ErrorFeedback
          data-plasmic-name={"errorFeedback"}
          data-plasmic-override={overrides.errorFeedback}
          className={classNames("__wab_instance", sty.errorFeedback, {
            [sty.errorFeedbackerror_apex]: hasVariant($state, "error", "apex"),
            [sty.errorFeedbackerror_cname]: hasVariant(
              $state,
              "error",
              "cname"
            ),
            [sty.errorFeedbackerror_error]: hasVariant(
              $state,
              "error",
              "error"
            ),
          })}
        >
          <div
            data-plasmic-name={"domainErrorMessage4"}
            data-plasmic-override={overrides.domainErrorMessage4}
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.domainErrorMessage4,
              {
                [sty.domainErrorMessage4error_apex]: hasVariant(
                  $state,
                  "error",
                  "apex"
                ),
                [sty.domainErrorMessage4error_cname]: hasVariant(
                  $state,
                  "error",
                  "cname"
                ),
                [sty.domainErrorMessage4error_error]: hasVariant(
                  $state,
                  "error",
                  "error"
                ),
              }
            )}
          >
            {hasVariant($state, "error", "error") ? (
              "Invalid configuration"
            ) : hasVariant($state, "error", "apex") ? (
              "Invalid configuration"
            ) : hasVariant($state, "error", "cname") ? (
              "Invalid configuration"
            ) : (
              <React.Fragment>
                <React.Fragment>
                  {"{yoursite.com} is already owned by another team. "}
                </React.Fragment>
                <span
                  className={"plasmic_default__all plasmic_default__span"}
                  style={{ textDecorationLine: "underline" }}
                >
                  {"Click here to request access"}
                </span>
                <React.Fragment>{"."}</React.Fragment>
              </React.Fragment>
            )}
          </div>
        </ErrorFeedback>
        <Stack__
          as={"div"}
          hasGap={true}
          className={classNames(projectcss.all, sty.freeBox__nPfIi, {
            [sty.freeBoxerror_apex__nPfIiWIbN7]: hasVariant(
              $state,
              "error",
              "apex"
            ),
            [sty.freeBoxerror_cname__nPfIiFiouZ]: hasVariant(
              $state,
              "error",
              "cname"
            ),
            [sty.freeBoxerror_success__nPfIiSUyAh]: hasVariant(
              $state,
              "error",
              "success"
            ),
          })}
        >
          <CheckCirclesvgIcon
            className={classNames(projectcss.all, sty.svg__t0WGq)}
            role={"img"}
          />

          <div
            className={classNames(
              projectcss.all,
              projectcss.__wab_text,
              sty.text__wBhQf,
              {
                [sty.texterror_cname__wBhQfFiouZ]: hasVariant(
                  $state,
                  "error",
                  "cname"
                ),
              }
            )}
          >
            {"Correctly configured!"}
          </div>
        </Stack__>
        <Stack__
          as={"div"}
          hasGap={true}
          className={classNames(projectcss.all, sty.freeBox__j4DNt, {
            [sty.freeBoxerror_cname__j4DNtFiouZ]: hasVariant(
              $state,
              "error",
              "cname"
            ),
            [sty.freeBoxerror_success__j4DNtSUyAh]: hasVariant(
              $state,
              "error",
              "success"
            ),
          })}
        >
          <Button
            data-plasmic-name={"refreshButton"}
            data-plasmic-override={overrides.refreshButton}
            caption={"Caption"}
            disabled={
              hasVariant($state, "refreshing", "refreshing") ? true : undefined
            }
            endIcon={
              <ChevronDownsvgIcon
                className={classNames(projectcss.all, sty.svg__dbKe3)}
                role={"img"}
              />
            }
            size={"wide"}
            startIcon={
              <RefreshsvgIcon
                className={classNames(projectcss.all, sty.svg__fkj25, {
                  [sty.svgerror_error__fkj25ByfXx]: hasVariant(
                    $state,
                    "error",
                    "error"
                  ),
                  [sty.svgrefreshing__fkj251Rn0P]: hasVariant(
                    $state,
                    "refreshing",
                    "refreshing"
                  ),
                })}
                role={"img"}
              />
            }
            type={["clear"]}
            withIcons={["startIcon"]}
          >
            {hasVariant($state, "refreshing", "refreshing")
              ? "Refreshing\u2026"
              : "Refresh"}
          </Button>
          <Button
            data-plasmic-name={"removeButton"}
            data-plasmic-override={overrides.removeButton}
            caption={"Caption"}
            color={"red"}
            endIcon={
              <ChevronDownsvgIcon
                className={classNames(projectcss.all, sty.svg__vkHmm)}
                role={"img"}
              />
            }
            size={"wide"}
            startIcon={
              <Trash2SvgIcon
                className={classNames(projectcss.all, sty.svg__xhMx)}
                role={"img"}
              />
            }
            type={["clear"]}
            withIcons={["startIcon"]}
          >
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__oH9Ce
              )}
            >
              {"Remove"}
            </div>
          </Button>
        </Stack__>
      </Stack__>
      <div
        className={classNames(projectcss.all, sty.freeBox__v8YX, {
          [sty.freeBoxerror_apex__v8YXWIbN7]: hasVariant(
            $state,
            "error",
            "apex"
          ),
          [sty.freeBoxerror_cname__v8YXFiouZ]: hasVariant(
            $state,
            "error",
            "cname"
          ),
          [sty.freeBoxerror_error__v8YXByfXx]: hasVariant(
            $state,
            "error",
            "error"
          ),
          [sty.freeBoxerror_success__v8YXSUyAh]: hasVariant(
            $state,
            "error",
            "success"
          ),
        })}
      >
        <div
          className={classNames(
            projectcss.all,
            projectcss.__wab_text,
            sty.text__mVvws,
            {
              [sty.texterror_apex__mVvwswIbN7]: hasVariant(
                $state,
                "error",
                "apex"
              ),
              [sty.texterror_cname__mVvwsFiouZ]: hasVariant(
                $state,
                "error",
                "cname"
              ),
              [sty.texterror_error__mVvwsByfXx]: hasVariant(
                $state,
                "error",
                "error"
              ),
              [sty.texterror_success__mVvwssUyAh]: hasVariant(
                $state,
                "error",
                "success"
              ),
              [sty.textsecondary__mVvwSzeL6W]: hasVariant(
                $state,
                "secondary",
                "secondary"
              ),
            }
          )}
        >
          {"Set the following records on your DNS provider to continue:"}
        </div>
        <Stack__
          as={"div"}
          hasGap={true}
          className={classNames(projectcss.all, sty.freeBox__jmREj)}
        >
          <button
            data-plasmic-name={"cnameTab"}
            data-plasmic-override={overrides.cnameTab}
            className={classNames(
              projectcss.all,
              projectcss.button,
              projectcss.__wab_text,
              sty.cnameTab,
              {
                [sty.cnameTaberror_apex]: hasVariant($state, "error", "apex"),
                [sty.cnameTaberror_cname]: hasVariant($state, "error", "cname"),
              }
            )}
            ref={(ref) => {
              $refs["cnameTab"] = ref;
            }}
          >
            <React.Fragment>
              <React.Fragment>{"CNAME Record "}</React.Fragment>
              <span
                className={"plasmic_default__all plasmic_default__span"}
                style={{ fontWeight: 400 }}
              >
                {"(subdomains)"}
              </span>
            </React.Fragment>
          </button>
          <button
            data-plasmic-name={"apexTab"}
            data-plasmic-override={overrides.apexTab}
            className={classNames(
              projectcss.all,
              projectcss.button,
              projectcss.__wab_text,
              sty.apexTab,
              {
                [sty.apexTaberror_apex]: hasVariant($state, "error", "apex"),
                [sty.apexTaberror_cname]: hasVariant($state, "error", "cname"),
              }
            )}
            ref={(ref) => {
              $refs["apexTab"] = ref;
            }}
          >
            <React.Fragment>
              <React.Fragment>{"A Record "}</React.Fragment>
              <span
                className={"plasmic_default__all plasmic_default__span"}
                style={{ fontWeight: 400 }}
              >
                {"(apex domain)"}
              </span>
            </React.Fragment>
          </button>
        </Stack__>
        <div
          className={classNames(projectcss.all, sty.freeBox___7NWq, {
            [sty.freeBoxerror_apex___7NWqWIbN7]: hasVariant(
              $state,
              "error",
              "apex"
            ),
            [sty.freeBoxerror_apex_secondary___7NWqWIbN7ZeL6W]:
              hasVariant($state, "secondary", "secondary") &&
              hasVariant($state, "error", "apex"),
            [sty.freeBoxerror_cname___7NWqFiouZ]: hasVariant(
              $state,
              "error",
              "cname"
            ),
            [sty.freeBoxerror_error___7NWqByfXx]: hasVariant(
              $state,
              "error",
              "error"
            ),
          })}
        >
          <div
            className={classNames(projectcss.all, sty.freeBox___1H9CF, {
              [sty.freeBoxerror_apex___1H9CFwIbN7]: hasVariant(
                $state,
                "error",
                "apex"
              ),
              [sty.freeBoxerror_cname___1H9CFFiouZ]: hasVariant(
                $state,
                "error",
                "cname"
              ),
            })}
          >
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__o1F3S,
                {
                  [sty.texterror_cname__o1F3SFiouZ]: hasVariant(
                    $state,
                    "error",
                    "cname"
                  ),
                }
              )}
            >
              {"Type"}
            </div>
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__eXNr
              )}
            >
              {"Name"}
            </div>
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__o16H
              )}
            >
              {"Value"}
            </div>
          </div>
          <div
            data-plasmic-name={"apexRow"}
            data-plasmic-override={overrides.apexRow}
            className={classNames(projectcss.all, sty.apexRow, {
              [sty.apexRowerror_apex]: hasVariant($state, "error", "apex"),
              [sty.apexRowerror_apex_secondary]:
                hasVariant($state, "secondary", "secondary") &&
                hasVariant($state, "error", "apex"),
              [sty.apexRowerror_cname]: hasVariant($state, "error", "cname"),
              [sty.apexRowerror_error]: hasVariant($state, "error", "error"),
            })}
          >
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__nkp3X,
                {
                  [sty.texterror_apex__nkp3XWIbN7]: hasVariant(
                    $state,
                    "error",
                    "apex"
                  ),
                }
              )}
            >
              {hasVariant($state, "error", "apex") ? "A" : "CNAME"}
            </div>
            <div
              data-plasmic-name={"name"}
              data-plasmic-override={overrides.name}
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.name,
                {
                  [sty.nameerror_apex]: hasVariant($state, "error", "apex"),
                  [sty.nameerror_cname]: hasVariant($state, "error", "cname"),
                }
              )}
            >
              {hasVariant($state, "error", "apex") ? "@" : "www"}
            </div>
            <div
              data-plasmic-name={"value"}
              data-plasmic-override={overrides.value}
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.value,
                {
                  [sty.valueerror_apex]: hasVariant($state, "error", "apex"),
                  [sty.valueerror_cname]: hasVariant($state, "error", "cname"),
                }
              )}
            >
              {hasVariant($state, "error", "apex")
                ? "76.76.21.21"
                : "cname.plasmicdev.com"}
            </div>
          </div>
          <div
            data-plasmic-name={"cnameRow"}
            data-plasmic-override={overrides.cnameRow}
            className={classNames(projectcss.all, sty.cnameRow, {
              [sty.cnameRowerror_apex]: hasVariant($state, "error", "apex"),
              [sty.cnameRowerror_cname]: hasVariant($state, "error", "cname"),
              [sty.cnameRowerror_error]: hasVariant($state, "error", "error"),
            })}
          >
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text___3Uvi0,
                {
                  [sty.texterror_apex___3Uvi0WIbN7]: hasVariant(
                    $state,
                    "error",
                    "apex"
                  ),
                  [sty.texterror_apex_secondary___3Uvi0WIbN7ZeL6W]:
                    hasVariant($state, "secondary", "secondary") &&
                    hasVariant($state, "error", "apex"),
                  [sty.texterror_cname___3Uvi0FiouZ]: hasVariant(
                    $state,
                    "error",
                    "cname"
                  ),
                }
              )}
            >
              {hasVariant($state, "error", "apex") ? "A" : "APEX"}
            </div>
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text__jnhOs,
                {
                  [sty.texterror_apex__jnhOswIbN7]: hasVariant(
                    $state,
                    "error",
                    "apex"
                  ),
                  [sty.texterror_cname__jnhOsFiouZ]: hasVariant(
                    $state,
                    "error",
                    "cname"
                  ),
                }
              )}
            >
              {hasVariant($state, "error", "apex") ? "@" : "@"}
            </div>
            <div
              className={classNames(
                projectcss.all,
                projectcss.__wab_text,
                sty.text___709Ap,
                {
                  [sty.texterror_apex___709ApwIbN7]: hasVariant(
                    $state,
                    "error",
                    "apex"
                  ),
                  [sty.texterror_cname___709ApFiouZ]: hasVariant(
                    $state,
                    "error",
                    "cname"
                  ),
                }
              )}
            >
              {hasVariant($state, "error", "apex")
                ? "76.76.21.21"
                : "76.76.21.21"}
            </div>
          </div>
        </div>
      </div>
    </Stack__>
  ) as React.ReactElement | null;
}

const PlasmicDescendants = {
  root: [
    "root",
    "label",
    "customDomainLabel",
    "openButton",
    "errorFeedback",
    "domainErrorMessage4",
    "refreshButton",
    "removeButton",
    "cnameTab",
    "apexTab",
    "apexRow",
    "name",
    "value",
    "cnameRow",
  ],

  label: ["label"],
  customDomainLabel: ["customDomainLabel"],
  openButton: ["openButton"],
  errorFeedback: ["errorFeedback", "domainErrorMessage4"],
  domainErrorMessage4: ["domainErrorMessage4"],
  refreshButton: ["refreshButton"],
  removeButton: ["removeButton"],
  cnameTab: ["cnameTab"],
  apexTab: ["apexTab"],
  apexRow: ["apexRow", "name", "value"],
  name: ["name"],
  value: ["value"],
  cnameRow: ["cnameRow"],
} as const;
type NodeNameType = keyof typeof PlasmicDescendants;
type DescendantsType<T extends NodeNameType> =
  (typeof PlasmicDescendants)[T][number];
type NodeDefaultElementType = {
  root: "div";
  label: "div";
  customDomainLabel: "div";
  openButton: typeof IconButton;
  errorFeedback: typeof ErrorFeedback;
  domainErrorMessage4: "div";
  refreshButton: typeof Button;
  removeButton: typeof Button;
  cnameTab: "button";
  apexTab: "button";
  apexRow: "div";
  name: "div";
  value: "div";
  cnameRow: "div";
};

type ReservedPropsType = "variants" | "args" | "overrides";
type NodeOverridesType<T extends NodeNameType> = Pick<
  PlasmicDomainCard__OverridesType,
  DescendantsType<T>
>;

type NodeComponentProps<T extends NodeNameType> =
  // Explicitly specify variants, args, and overrides as objects
  {
    variants?: PlasmicDomainCard__VariantsArgs;
    args?: PlasmicDomainCard__ArgsType;
    overrides?: NodeOverridesType<T>;
  } & Omit<PlasmicDomainCard__VariantsArgs, ReservedPropsType> & // Specify variants directly as props
    /* Specify args directly as props*/ Omit<
      PlasmicDomainCard__ArgsType,
      ReservedPropsType
    > &
    /* Specify overrides for each element directly as props*/ Omit<
      NodeOverridesType<T>,
      ReservedPropsType | VariantPropType | ArgPropType
    > &
    /* Specify props for the root element*/ Omit<
      Partial<React.ComponentProps<NodeDefaultElementType[T]>>,
      ReservedPropsType | VariantPropType | ArgPropType | DescendantsType<T>
    >;

function makeNodeComponent<NodeName extends NodeNameType>(nodeName: NodeName) {
  type PropsType = NodeComponentProps<NodeName> & { key?: React.Key };
  const func = function <T extends PropsType>(
    props: T & StrictProps<T, PropsType>
  ) {
    const { variants, args, overrides } = React.useMemo(
      () =>
        deriveRenderOpts(props, {
          name: nodeName,
          descendantNames: PlasmicDescendants[nodeName],
          internalArgPropNames: PlasmicDomainCard__ArgProps,
          internalVariantPropNames: PlasmicDomainCard__VariantProps,
        }),
      [props, nodeName]
    );
    return PlasmicDomainCard__RenderFunc({
      variants,
      args,
      overrides,
      forNode: nodeName,
    });
  };
  if (nodeName === "root") {
    func.displayName = "PlasmicDomainCard";
  } else {
    func.displayName = `PlasmicDomainCard.${nodeName}`;
  }
  return func;
}

export const PlasmicDomainCard = Object.assign(
  // Top-level PlasmicDomainCard renders the root element
  makeNodeComponent("root"),
  {
    // Helper components rendering sub-elements
    label: makeNodeComponent("label"),
    customDomainLabel: makeNodeComponent("customDomainLabel"),
    openButton: makeNodeComponent("openButton"),
    errorFeedback: makeNodeComponent("errorFeedback"),
    domainErrorMessage4: makeNodeComponent("domainErrorMessage4"),
    refreshButton: makeNodeComponent("refreshButton"),
    removeButton: makeNodeComponent("removeButton"),
    cnameTab: makeNodeComponent("cnameTab"),
    apexTab: makeNodeComponent("apexTab"),
    apexRow: makeNodeComponent("apexRow"),
    _name: makeNodeComponent("name"),
    value: makeNodeComponent("value"),
    cnameRow: makeNodeComponent("cnameRow"),

    // Metadata about props expected for PlasmicDomainCard
    internalVariantProps: PlasmicDomainCard__VariantProps,
    internalArgProps: PlasmicDomainCard__ArgProps,
  }
);

export default PlasmicDomainCard;
/* prettier-ignore-end */
